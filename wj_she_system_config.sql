-- ===========================
-- 插件 wj_she 系统配置表
-- ===========================

DROP TABLE IF EXISTS `wj_she_system_config`;
CREATE TABLE `wj_she_system_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `config_name` varchar(100) NOT NULL DEFAULT '' COMMENT '配置名称',
  `config_key` varchar(50) NOT NULL DEFAULT '' COMMENT '配置标识',
  
  -- 收货门店信息
  `store_receiver_name` varchar(50) NOT NULL DEFAULT '' COMMENT '收货人姓名',
  `store_receiver_phone` varchar(20) NOT NULL DEFAULT '' COMMENT '收货人电话',
  `store_province_id` int(11) NOT NULL DEFAULT 0 COMMENT '省份ID',
  `store_province_name` varchar(50) NOT NULL DEFAULT '' COMMENT '省份名称',
  `store_city_id` int(11) NOT NULL DEFAULT 0 COMMENT '城市ID',
  `store_city_name` varchar(50) NOT NULL DEFAULT '' COMMENT '城市名称',
  `store_district_id` int(11) NOT NULL DEFAULT 0 COMMENT '区县ID',
  `store_district_name` varchar(50) NOT NULL DEFAULT '' COMMENT '区县名称',
  `store_address` varchar(200) NOT NULL DEFAULT '' COMMENT '详细地址',
  `store_full_address` varchar(300) NOT NULL DEFAULT '' COMMENT '完整地址',
  
  -- 平台客服信息
  `service_qr_code` varchar(500) NOT NULL DEFAULT '' COMMENT '客服二维码图片路径',
  `service_phone` varchar(20) NOT NULL DEFAULT '' COMMENT '客服电话',
  `service_work_time` varchar(100) NOT NULL DEFAULT '8:00-20:00' COMMENT '客服工作时间',
  `service_description` varchar(200) NOT NULL DEFAULT '人工客服工作时间为8:00-20:00，咨询高峰期会有延迟' COMMENT '客服说明',
  
  -- 云洋快递配置
  `yunyang_appid` varchar(100) NOT NULL DEFAULT '' COMMENT '云洋快递AppID',
  `yunyang_key` varchar(200) NOT NULL DEFAULT '' COMMENT '云洋快递Key',
  `yunyang_api_url` varchar(200) NOT NULL DEFAULT '' COMMENT '云洋快递API地址',
  `yunyang_enabled` tinyint(1) NOT NULL DEFAULT 1 COMMENT '云洋快递是否启用 0:禁用 1:启用',
  
  -- 系统字段
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态 0:禁用 1:启用',
  `is_default` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否默认配置 0:否 1:是',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `remark` varchar(500) NOT NULL DEFAULT '' COMMENT '备注说明',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_config_key` (`config_key`),
  KEY `idx_status` (`status`),
  KEY `idx_is_default` (`is_default`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='wj_she插件系统配置表';

-- 插入默认配置数据
INSERT INTO `wj_she_system_config` (
  `config_name`,
  `config_key`,
  `store_receiver_name`,
  `store_receiver_phone`,
  `store_province_id`,
  `store_province_name`,
  `store_city_id`,
  `store_city_name`,
  `store_district_id`,
  `store_district_name`,
  `store_address`,
  `store_full_address`,
  `service_qr_code`,
  `service_phone`,
  `service_work_time`,
  `service_description`,
  `yunyang_appid`,
  `yunyang_key`,
  `yunyang_api_url`,
  `yunyang_enabled`,
  `status`,
  `is_default`,
  `sort`,
  `remark`,
  `create_time`,
  `update_time`
) VALUES (
  '默认系统配置',
  'default_config',
  '',
  '',
  0,
  '',
  0,
  '',
  0,
  '',
  '',
  '',
  '',
  '',
  '8:00-20:00',
  '人工客服工作时间为8:00-20:00，咨询高峰期会有延迟',
  '',
  '',
  '',
  1,
  1,
  1,
  1,
  '系统默认配置',
  UNIX_TIMESTAMP(),
  UNIX_TIMESTAMP()
);

-- 创建配置项说明表（可选，用于管理配置项的详细说明）
DROP TABLE IF EXISTS `wj_she_config_fields`;
CREATE TABLE `wj_she_config_fields` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `field_name` varchar(50) NOT NULL DEFAULT '' COMMENT '字段名称',
  `field_label` varchar(100) NOT NULL DEFAULT '' COMMENT '字段标签',
  `field_type` varchar(20) NOT NULL DEFAULT 'text' COMMENT '字段类型 text/textarea/select/image/number',
  `field_group` varchar(50) NOT NULL DEFAULT '' COMMENT '字段分组',
  `is_required` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否必填 0:否 1:是',
  `default_value` varchar(500) NOT NULL DEFAULT '' COMMENT '默认值',
  `field_options` text COMMENT '字段选项(JSON格式)',
  `field_desc` varchar(200) NOT NULL DEFAULT '' COMMENT '字段说明',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态 0:禁用 1:启用',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_field_group` (`field_group`),
  KEY `idx_sort` (`sort`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='wj_she插件配置字段说明表';

-- 插入字段配置说明
INSERT INTO `wj_she_config_fields` (`field_name`, `field_label`, `field_type`, `field_group`, `is_required`, `default_value`, `field_desc`, `sort`, `create_time`) VALUES
-- 收货门店信息组
('store_receiver_name', '收货人姓名', 'text', 'store_info', 1, '', '门店收货联系人姓名', 1, UNIX_TIMESTAMP()),
('store_receiver_phone', '收货人电话', 'text', 'store_info', 1, '', '门店收货联系人电话', 2, UNIX_TIMESTAMP()),
('store_province_name', '省份', 'text', 'store_info', 1, '', '门店所在省份', 3, UNIX_TIMESTAMP()),
('store_city_name', '城市', 'text', 'store_info', 1, '', '门店所在城市', 4, UNIX_TIMESTAMP()),
('store_district_name', '区县', 'text', 'store_info', 1, '', '门店所在区县', 5, UNIX_TIMESTAMP()),
('store_address', '详细地址', 'textarea', 'store_info', 1, '', '门店详细地址', 6, UNIX_TIMESTAMP()),

-- 平台客服信息组
('service_qr_code', '客服二维码', 'image', 'service_info', 0, '', '客服微信二维码图片', 7, UNIX_TIMESTAMP()),
('service_phone', '客服电话', 'text', 'service_info', 0, '', '客服联系电话', 8, UNIX_TIMESTAMP()),
('service_work_time', '工作时间', 'text', 'service_info', 0, '8:00-20:00', '客服工作时间', 9, UNIX_TIMESTAMP()),
('service_description', '客服说明', 'textarea', 'service_info', 0, '人工客服工作时间为8:00-20:00，咨询高峰期会有延迟', '客服说明文字', 10, UNIX_TIMESTAMP()),

-- 云洋快递配置组
('yunyang_appid', '云洋快递AppID', 'text', 'yunyang_config', 1, '', '云洋快递平台分配的AppID', 11, UNIX_TIMESTAMP()),
('yunyang_key', '云洋快递Key', 'text', 'yunyang_config', 1, '', '云洋快递平台分配的密钥', 12, UNIX_TIMESTAMP()),
('yunyang_api_url', 'API地址', 'text', 'yunyang_config', 0, '', '云洋快递API接口地址', 13, UNIX_TIMESTAMP()),
('yunyang_enabled', '是否启用', 'select', 'yunyang_config', 0, '1', '是否启用云洋快递服务', 14, UNIX_TIMESTAMP());

-- 更新字段选项
UPDATE `wj_she_config_fields` SET `field_options` = '{"1":"启用","0":"禁用"}' WHERE `field_name` = 'yunyang_enabled';
