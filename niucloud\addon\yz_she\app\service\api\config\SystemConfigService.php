<?php
// +----------------------------------------------------------------------
// | Niucloud-admin 企业快速开发的多应用管理平台
// +----------------------------------------------------------------------
// | 官方网址：https://www.niucloud.com
// +----------------------------------------------------------------------
// | niucloud团队 版权所有 开源版本可自由商用
// +----------------------------------------------------------------------
// | Author: Niucloud Team
// +----------------------------------------------------------------------

namespace addon\yz_she\app\service\api\config;

use addon\yz_she\app\model\config\SystemConfig;
use core\base\BaseApiService;

/**
 * 系统配置API服务层
 * Class SystemConfigService
 * @package addon\yz_she\app\service\api\config
 */
class SystemConfigService extends BaseApiService
{
    public function __construct()
    {
        parent::__construct();
        $this->model = new SystemConfig();
    }

    /**
     * 获取当前有效的系统配置
     * @return array
     */
    public function getCurrentConfig()
    {
        $field = 'id, store_receiver_name, store_receiver_phone, store_province_name, store_city_name, store_district_name, store_address, service_qr_code, service_phone, yunyang_appid, yunyang_key, yunyang_api_url, yunyang_enabled';
        
        $info = $this->model->field($field)->order('id desc')->findOrEmpty()->toArray();
        
        // 如果没有配置，返回默认配置
        if (empty($info)) {
            return $this->getDefaultConfig();
        }
        
        return $info;
    }

    /**
     * 获取收货门店信息
     * @return array
     */
    public function getStoreInfo()
    {
        $config = $this->getCurrentConfig();
        return [
            'store_receiver_name' => $config['store_receiver_name'],
            'store_receiver_phone' => $config['store_receiver_phone'],
            'store_province_name' => $config['store_province_name'],
            'store_city_name' => $config['store_city_name'],
            'store_district_name' => $config['store_district_name'],
            'store_address' => $config['store_address']
        ];
    }

    /**
     * 获取客服信息
     * @return array
     */
    public function getServiceInfo()
    {
        $config = $this->getCurrentConfig();
        return [
            'service_qr_code' => $config['service_qr_code'],
            'service_phone' => $config['service_phone']
        ];
    }

    /**
     * 获取云洋快递配置
     * @return array
     */
    public function getYunyangConfig()
    {
        $config = $this->getCurrentConfig();
        return [
            'yunyang_appid' => $config['yunyang_appid'],
            'yunyang_key' => $config['yunyang_key'],
            'yunyang_api_url' => $config['yunyang_api_url'],
            'yunyang_enabled' => $config['yunyang_enabled']
        ];
    }

    /**
     * 获取默认配置
     * @return array
     */
    private function getDefaultConfig()
    {
        return [
            'id' => 0,
            'store_receiver_name' => '',
            'store_receiver_phone' => '',
            'store_province_name' => '',
            'store_city_name' => '',
            'store_district_name' => '',
            'store_address' => '',
            'service_qr_code' => '',
            'service_phone' => '',
            'yunyang_appid' => '',
            'yunyang_key' => '',
            'yunyang_api_url' => '',
            'yunyang_enabled' => 1
        ];
    }

    /**
     * 获取完整收货地址
     * @return string
     */
    public function getFullStoreAddress()
    {
        $config = $this->getCurrentConfig();
        return $config['store_province_name'] . $config['store_city_name'] . $config['store_district_name'] . $config['store_address'];
    }

    /**
     * 检查云洋快递是否启用
     * @return bool
     */
    public function isYunyangEnabled()
    {
        $config = $this->getCurrentConfig();
        return $config['yunyang_enabled'] == 1;
    }
}
