<template>
  <view class="faq-page bg-[var(--page-bg-color)] min-h-[100vh]" :style="themeColor()">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-content">
        <view class="back-btn" @click="goBack">
          <u-icon name="arrow-left" color="#333" size="18"></u-icon>
        </view>
        <view class="navbar-title">热门问题</view>
        <view class="placeholder"></view>
      </view>
    </view>

    <!-- 页面内容 -->
    <view class="page-content">
      <!-- FAQ列表 -->
      <view class="faq-container">
        <view
          class="faq-item"
          v-for="(item, index) in faqList"
          :key="index"
          @click="toggleFaq(index)"
        >
          <view class="faq-question">
            <view class="question-number">{{ index + 1 }}.</view>
            <view class="question-text">{{ item.question }}</view>
            <view class="arrow-icon" :class="{ 'expanded': item.expanded }">
              <u-icon name="arrow-down" color="#999" size="14"></u-icon>
            </view>
          </view>

          <view class="faq-answer" v-if="item.expanded">
            <text class="answer-text">{{ item.answer }}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { themeColor } from '@/utils/common'

// FAQ数据
const faqList = ref([
  {
    question: '哪些城市/地区支持回收服务？',
    answer: '您好，品牌鞋服回收暂时只开放了江浙沪地区，潮鞋潮服全国可回收。',
    expanded: true // 默认展开第一个
  },
  {
    question: '订单提交后能取消吗？',
    answer: '订单提交后，在商品未被取件前可以取消。如果商品已经被取件，则无法取消订单。',
    expanded: false
  },
  {
    question: '不同意估价，衣服怎么退回？',
    answer: '如果您不同意我们的估价，可以选择退回商品。我们会免费为您寄回，您只需要承担退回的运费即可。',
    expanded: false
  },
  {
    question: '我的衣服有轻微瑕疵，还能回收吗？',
    answer: '轻微瑕疵的衣服仍然可以回收，但会根据瑕疵程度调整回收价格。建议您在提交订单时详细描述商品状况。',
    expanded: false
  }
])

// 切换FAQ展开/收起
const toggleFaq = (index: number) => {
  faqList.value[index].expanded = !faqList.value[index].expanded
}

// 返回上一页
const goBack = () => {
  uni.navigateBack()
}

onLoad(() => {
  // 页面加载时的初始化操作
})
</script>

<style lang="scss" scoped>
.faq-page {
  .custom-navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 999;
    background: #fff;
    border-bottom: 1rpx solid #f0f0f0;

    .navbar-content {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 88rpx;
      padding: 0 var(--sidebar-m);
      padding-top: var(--status-bar-height, 0);

      .back-btn {
        width: 60rpx;
        height: 60rpx;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .navbar-title {
        font-size: 32rpx;
        font-weight: 500;
        color: #333;
      }

      .placeholder {
        width: 60rpx;
      }
    }
  }

  .page-content {
    padding-top: calc(88rpx + var(--status-bar-height, 0));
    padding: calc(88rpx + var(--status-bar-height, 0)) var(--sidebar-m) var(--sidebar-m);
  }

  .faq-container {
    background: #fff;
    border-radius: var(--rounded-big);
    overflow: hidden;

    .faq-item {
      border-bottom: 1rpx solid #f5f5f5;

      &:last-child {
        border-bottom: none;
      }

      .faq-question {
        display: flex;
        align-items: center;
        padding: 40rpx var(--pad-sidebar-m);
        cursor: pointer;

        .question-number {
          font-size: 28rpx;
          color: #333;
          font-weight: 500;
          margin-right: 16rpx;
          flex-shrink: 0;
        }

        .question-text {
          flex: 1;
          font-size: 28rpx;
          color: #333;
          line-height: 1.5;
          margin-right: 20rpx;
        }

        .arrow-icon {
          transition: transform 0.3s ease;

          &.expanded {
            transform: rotate(180deg);
          }
        }
      }

      .faq-answer {
        padding: 0 var(--pad-sidebar-m) 40rpx;
        padding-left: calc(var(--pad-sidebar-m) + 44rpx); // 对齐问题文本

        .answer-text {
          font-size: 26rpx;
          color: #666;
          line-height: 1.6;
        }
      }
    }
  }
}

// 获取状态栏高度
.faq-page {
  --status-bar-height: env(safe-area-inset-top);
}
</style>