<template>
  <view class="faq-page bg-[#f6f6f6] min-h-[100vh]">


    <!-- 页面内容 -->
    <view class="page-content">
      <!-- FAQ列表 -->
      <view class="faq-container">
        <view
          class="faq-item"
          v-for="(item, index) in faqList"
          :key="index"
          @click="toggleFaq(index)"
        >
          <view class="faq-question">
            <view class="question-number">{{ index + 1 }}.</view>
            <view class="question-text">{{ item.question }}</view>
            <view class="arrow-icon" :class="{ 'expanded': item.expanded }">
              <u-icon name="arrow-down" color="#999" size="14"></u-icon>
            </view>
          </view>

          <view class="faq-answer" v-if="item.expanded">
            <text class="answer-text">{{ item.answer }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部客服按钮 -->
    <view class="bottom-service">
      <view class="service-buttons">
        <view class="service-btn phone-btn" @click="callPhone">
          <u-icon name="phone" color="#666" size="20"></u-icon>
          <text class="btn-text">电话客服</text>
        </view>
        <view class="service-btn online-btn" @click="openOnlineService">
          <u-icon name="chat" color="#666" size="20"></u-icon>
          <text class="btn-text">在线客服</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { onLoad } from '@dcloudio/uni-app'

// FAQ数据
const faqList = ref([
  {
    question: '哪些城市/地区支持回收服务？',
    answer: '您好，品牌鞋服回收暂时只开放了江浙沪地区，潮鞋潮服全国可回收。',
    expanded: true // 默认展开第一个
  },
  {
    question: '订单提交后能取消吗？',
    answer: '订单提交后，在商品未被取件前可以取消。如果商品已经被取件，则无法取消订单。',
    expanded: false
  },
  {
    question: '不同意估价，衣服怎么退回？',
    answer: '如果您不同意我们的估价，可以选择退回商品。我们会免费为您寄回，您只需要承担退回的运费即可。',
    expanded: false
  },
  {
    question: '我的衣服有轻微瑕疵，还能回收吗？',
    answer: '轻微瑕疵的衣服仍然可以回收，但会根据瑕疵程度调整回收价格。建议您在提交订单时详细描述商品状况。',
    expanded: false
  }
])

// 切换FAQ展开/收起
const toggleFaq = (index: number) => {
  faqList.value[index].expanded = !faqList.value[index].expanded
}

// 返回上一页
const goBack = () => {
  uni.navigateBack()
}

// 拨打客服电话
const callPhone = () => {
  uni.makePhoneCall({
    phoneNumber: '************', // 替换为实际的客服电话
    success: () => {
      console.log('拨打电话成功')
    },
    fail: (err) => {
      console.log('拨打电话失败', err)
      uni.showToast({
        title: '拨打失败',
        icon: 'none'
      })
    }
  })
}

// 打开在线客服
const openOnlineService = () => {
  // 这里可以根据实际需求实现在线客服功能
  // 例如跳转到客服页面或打开客服聊天窗口
  uni.showToast({
    title: '正在连接客服...',
    icon: 'loading',
    duration: 1500
  })

  // 示例：跳转到客服页面
  // uni.navigateTo({
  //   url: '/pages/customer-service/chat'
  // })
}

onLoad(() => {
  // 页面加载时的初始化操作
})
</script>

<style lang="scss" scoped>
.faq-page {
  .custom-navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 999;
    background: #fff;
    border-bottom: 1rpx solid #f0f0f0;

    .navbar-content {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 88rpx;
      padding: 0 20rpx;
      padding-top: var(--status-bar-height, 0);

      .back-btn {
        width: 60rpx;
        height: 60rpx;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .navbar-title {
        font-size: 32rpx;
        font-weight: 500;
        color: #333;
      }

      .placeholder {
        width: 60rpx;
      }
    }
  }

  .page-content {
    padding: 20rpx 20rpx 140rpx; // 底部留出按钮空间
  }

  .faq-container {
    background: #fff;
    border-radius: 24rpx;
    overflow: hidden;

    .faq-item {
      border-bottom: 1rpx solid #f5f5f5;

      &:last-child {
        border-bottom: none;
      }

      .faq-question {
        display: flex;
        align-items: center;
        padding: 40rpx 24rpx;
        cursor: pointer;

        .question-number {
          font-size: 28rpx;
          color: #333;
          font-weight: 500;
          margin-right: 16rpx;
          flex-shrink: 0;
        }

        .question-text {
          flex: 1;
          font-size: 28rpx;
          color: #333;
          line-height: 1.5;
          margin-right: 20rpx;
        }

        .arrow-icon {
          transition: transform 0.3s ease;

          &.expanded {
            transform: rotate(180deg);
          }
        }
      }

      .faq-answer {
        padding: 0 24rpx 40rpx;
        padding-left: calc(24rpx + 44rpx); // 对齐问题文本

        .answer-text {
          font-size: 26rpx;
          color: #666;
          line-height: 1.6;
        }
      }
    }
  }

  // 底部客服按钮
  .bottom-service {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: #fff;
    border-top: 1rpx solid #f0f0f0;
    padding: 20rpx;
    padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
    z-index: 999;

    .service-buttons {
      display: flex;
      gap: 20rpx;

      .service-btn {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 24rpx 20rpx;
        background: #f8f8f8;
        border-radius: 16rpx;
        border: 1rpx solid #e8e8e8;

        .btn-text {
          font-size: 24rpx;
          color: #666;
          margin-top: 8rpx;
        }

        &:active {
          background: #f0f0f0;
        }
      }

      .phone-btn {
        &:active {
          background: #e8f4fd;
        }
      }

      .online-btn {
        &:active {
          background: #e8f4fd;
        }
      }
    }
  }
}

// 获取状态栏高度
.faq-page {
  --status-bar-height: env(safe-area-inset-top);
}
</style>