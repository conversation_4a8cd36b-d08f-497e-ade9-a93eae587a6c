<template>
  <view class="faq-page bg-[#f6f6f6] min-h-[100vh]">
    <!-- 顶部二维码组件 -->
    <view class="qr-section">
      <view class="qr-container">
        <view class="qr-code-wrapper">
          <view class="qr-code-placeholder">
            <!-- 二维码占位符 -->
            <view class="qr-placeholder-bg"></view>
          </view>
        </view>
        <view class="qr-info">
          <view class="qr-title">
            <u-icon name="chat" color="#fff" size="18"></u-icon>
            <text class="title-text">1对1微信客服</text>
          </view>
          <text class="qr-desc">长按识别二维码添加</text>
        </view>
      </view>
    </view>

    <!-- 服务时间说明 -->
    <view class="service-time-section">
      <text class="service-time-text">人工客服工作时间为8:00-20:00，咨询高峰期会有延迟</text>
    </view>

    <!-- 热门问题标题 -->
    <view class="faq-header">
      <text class="faq-header-title">热门问题</text>
      <view class="faq-header-line"></view>
    </view>

    <!-- 页面内容 -->
    <view class="page-content">
      <!-- FAQ列表 -->
      <view class="faq-container">
        <view
          class="faq-item"
          v-for="(item, index) in faqList"
          :key="index"
          @click="toggleFaq(index)"
        >
          <view class="faq-question">
            <view class="question-number">{{ index + 1 }}.</view>
            <view class="question-text">{{ item.question }}</view>
            <view class="arrow-icon" :class="{ 'expanded': item.expanded }">
              <u-icon name="arrow-down" color="#999" size="14"></u-icon>
            </view>
          </view>

          <view class="faq-answer" v-if="item.expanded">
            <text class="answer-text">{{ item.answer }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部客服按钮 -->
    <view class="bottom-service">
      <view class="service-buttons">
        <view class="service-btn phone-btn" @click="callPhone">
          <u-icon name="phone" color="#666" size="20"></u-icon>
          <text class="btn-text">电话客服</text>
        </view>
        <view class="service-btn online-btn" @click="openOnlineService">
          <u-icon name="chat" color="#666" size="20"></u-icon>
          <text class="btn-text">在线客服</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { onLoad } from '@dcloudio/uni-app'

// FAQ数据
const faqList = ref([
  {
    question: '哪些城市/地区支持回收服务？',
    answer: '您好，品牌鞋服回收暂时只开放了江浙沪地区，潮鞋潮服全国可回收。',
    expanded: true // 默认展开第一个
  },
  {
    question: '订单提交后能取消吗？',
    answer: '订单提交后，在商品未被取件前可以取消。如果商品已经被取件，则无法取消订单。',
    expanded: false
  },
  {
    question: '不同意估价，衣服怎么退回？',
    answer: '如果您不同意我们的估价，可以选择退回商品。我们会免费为您寄回，您只需要承担退回的运费即可。',
    expanded: false
  },
  {
    question: '我的衣服有轻微瑕疵，还能回收吗？',
    answer: '轻微瑕疵的衣服仍然可以回收，但会根据瑕疵程度调整回收价格。建议您在提交订单时详细描述商品状况。',
    expanded: false
  }
])

// 切换FAQ展开/收起
const toggleFaq = (index: number) => {
  faqList.value[index].expanded = !faqList.value[index].expanded
}

// 返回上一页
const goBack = () => {
  uni.navigateBack()
}

// 拨打客服电话
const callPhone = () => {
  uni.makePhoneCall({
    phoneNumber: '************', // 替换为实际的客服电话
    success: () => {
      console.log('拨打电话成功')
    },
    fail: (err) => {
      console.log('拨打电话失败', err)
      uni.showToast({
        title: '拨打失败',
        icon: 'none'
      })
    }
  })
}

// 打开在线客服
const openOnlineService = () => {
  // 这里可以根据实际需求实现在线客服功能
  // 例如跳转到客服页面或打开客服聊天窗口
  uni.showToast({
    title: '正在连接客服...',
    icon: 'loading',
    duration: 1500
  })

  // 示例：跳转到客服页面
  // uni.navigateTo({
  //   url: '/pages/customer-service/chat'
  // })
}

onLoad(() => {
  // 页面加载时的初始化操作
})
</script>

<style lang="scss" scoped>
.faq-page {
  .custom-navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 999;
    background: #fff;
    border-bottom: 1rpx solid #f0f0f0;

    .navbar-content {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 88rpx;
      padding: 0 20rpx;
      padding-top: var(--status-bar-height, 0);

      .back-btn {
        width: 60rpx;
        height: 60rpx;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .navbar-title {
        font-size: 32rpx;
        font-weight: 500;
        color: #333;
      }

      .placeholder {
        width: 60rpx;
      }
    }
  }

  // 顶部二维码组件
  .qr-section {
    background: linear-gradient(180deg, #4A90E2 0%, #87CEEB 100%);
    padding: 40rpx 20rpx 60rpx;
    position: relative;
    overflow: hidden;

    // 添加装饰性背景元素
    &::before {
      content: '';
      position: absolute;
      top: -50%;
      right: -20%;
      width: 200rpx;
      height: 200rpx;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 50%;
    }

    &::after {
      content: '';
      position: absolute;
      bottom: -30%;
      left: -10%;
      width: 150rpx;
      height: 150rpx;
      background: rgba(255, 255, 255, 0.08);
      border-radius: 50%;
    }

    .qr-container {
      position: relative;
      z-index: 2;
      display: flex;
      flex-direction: column;
      align-items: center;

      .qr-code-wrapper {
        margin-bottom: 30rpx;

        .qr-code-placeholder {
          width: 200rpx;
          height: 200rpx;
          background: #fff;
          border-radius: 20rpx;
          padding: 20rpx;
          box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);

          .qr-placeholder-bg {
            width: 100%;
            height: 100%;
            background: #f0f0f0;
            border-radius: 12rpx;
            position: relative;

            // 添加二维码样式的装饰
            &::before {
              content: '';
              position: absolute;
              top: 20rpx;
              left: 20rpx;
              width: 30rpx;
              height: 30rpx;
              background: #333;
              border-radius: 4rpx;
            }

            &::after {
              content: '';
              position: absolute;
              bottom: 20rpx;
              right: 20rpx;
              width: 30rpx;
              height: 30rpx;
              background: #333;
              border-radius: 4rpx;
            }
          }
        }
      }

      .qr-info {
        text-align: center;
        margin-bottom: 30rpx;

        .qr-title {
          display: flex;
          align-items: center;
          justify-content: center;
          margin-bottom: 12rpx;

          .title-text {
            font-size: 32rpx;
            color: #fff;
            font-weight: 500;
            margin-left: 12rpx;
          }
        }

        .qr-desc {
          font-size: 26rpx;
          color: rgba(255, 255, 255, 0.9);
        }
      }

      .service-time {
        background: rgba(255, 255, 255, 0.15);
        padding: 20rpx 30rpx;
        border-radius: 30rpx;
        backdrop-filter: blur(10rpx);

        .time-text {
          font-size: 24rpx;
          color: rgba(255, 255, 255, 0.95);
          line-height: 1.4;
        }
      }
    }
  }

  // 服务时间说明区域
  .service-time-section {
    padding: 30rpx 20rpx;
    background: #fff;

    .service-time-text {
      font-size: 26rpx;
      color: #666;
      line-height: 1.5;
      text-align: center;
    }
  }

  // 热门问题标题区域
  .faq-header {
    padding: 40rpx 20rpx 20rpx;
    background: #fff;

    .faq-header-title {
      font-size: 32rpx;
      color: #333;
      font-weight: 600;
      margin-bottom: 16rpx;
    }

    .faq-header-line {
      width: 60rpx;
      height: 6rpx;
      background: #4A90E2;
      border-radius: 3rpx;
    }
  }

  .page-content {
    padding: 0 20rpx 140rpx; // 底部留出按钮空间
    background: #fff;
  }

  .faq-container {
    background: #fff;

    .faq-item {
      border-bottom: 1rpx solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }

      .faq-question {
        display: flex;
        align-items: center;
        padding: 32rpx 20rpx;
        cursor: pointer;

        .question-number {
          font-size: 28rpx;
          color: #333;
          font-weight: 500;
          margin-right: 12rpx;
          flex-shrink: 0;
        }

        .question-text {
          flex: 1;
          font-size: 28rpx;
          color: #333;
          line-height: 1.5;
          margin-right: 20rpx;
        }

        .arrow-icon {
          transition: transform 0.3s ease;

          &.expanded {
            transform: rotate(180deg);
          }
        }
      }

      .faq-answer {
        padding: 0 20rpx 32rpx 52rpx; // 左边距对齐问题文本
        background: #fff;

        .answer-text {
          font-size: 26rpx;
          color: #666;
          line-height: 1.6;
        }
      }
    }
  }

  // 底部客服按钮
  .bottom-service {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: #fff;
    border-top: 1rpx solid #f0f0f0;
    padding: 20rpx;
    padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
    z-index: 999;

    .service-buttons {
      display: flex;
      gap: 20rpx;

      .service-btn {
        flex: 1;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        padding: 24rpx 20rpx;
        background: #f8f8f8;
        border-radius: 16rpx;
        border: 1rpx solid #e8e8e8;

        .btn-text {
          font-size: 24rpx;
          color: #666;
          margin-left: 12rpx;
        }

        &:active {
          background: #f0f0f0;
        }
      }

      .phone-btn {
        &:active {
          background: #e8f4fd;
        }
      }

      .online-btn {
        &:active {
          background: #e8f4fd;
        }
      }
    }
  }
}

// 获取状态栏高度
.faq-page {
  --status-bar-height: env(safe-area-inset-top);
}
</style>