<?php
// +----------------------------------------------------------------------
// | Niucloud-admin 企业快速开发的多应用管理平台
// +----------------------------------------------------------------------
// | 官方网址：https://www.niucloud.com
// +----------------------------------------------------------------------
// | niucloud团队 版权所有 开源版本可自由商用
// +----------------------------------------------------------------------
// | Author: Niucloud Team
// +----------------------------------------------------------------------

namespace addon\yz_she\app\model\config;

use core\base\BaseModel;

/**
 * 系统配置模型
 * Class SystemConfig
 * @package addon\yz_she\app\model\config
 */
class SystemConfig extends BaseModel
{
    /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'id';

    /**
     * 模型名称
     * @var string
     */
    protected $name = 'yz_she_system_config';

    /**
     * 自动写入时间戳字段
     * @var string
     */
    protected $autoWriteTimestamp = true;

    /**
     * 创建时间字段
     * @var string
     */
    protected $createTime = 'create_time';

    /**
     * 更新时间字段
     * @var string
     */
    protected $updateTime = 'update_time';

    /**
     * 字段类型转换
     * @var array
     */
    protected $type = [
        'yunyang_enabled' => 'integer',
        'create_time' => 'timestamp',
        'update_time' => 'timestamp'
    ];

    /**
     * 获取器 - 格式化创建时间
     * @param $value
     * @return false|string
     */
    public function getCreateTimeAttr($value)
    {
        return $value ? date('Y-m-d H:i:s', $value) : '';
    }

    /**
     * 获取器 - 格式化更新时间
     * @param $value
     * @return false|string
     */
    public function getUpdateTimeAttr($value)
    {
        return $value ? date('Y-m-d H:i:s', $value) : '';
    }

    /**
     * 获取器 - 云洋快递状态文本
     * @param $value
     * @param $data
     * @return string
     */
    public function getYunyangEnabledTextAttr($value, $data)
    {
        return $data['yunyang_enabled'] == 1 ? '启用' : '禁用';
    }

    /**
     * 搜索器 - 收货人姓名
     * @param $query
     * @param $value
     */
    public function searchStoreReceiverNameAttr($query, $value)
    {
        if ($value) {
            $query->where('store_receiver_name', 'like', '%' . $value . '%');
        }
    }

    /**
     * 搜索器 - 收货人电话
     * @param $query
     * @param $value
     */
    public function searchStoreReceiverPhoneAttr($query, $value)
    {
        if ($value) {
            $query->where('store_receiver_phone', 'like', '%' . $value . '%');
        }
    }

    /**
     * 搜索器 - 省份
     * @param $query
     * @param $value
     */
    public function searchStoreProvinceNameAttr($query, $value)
    {
        if ($value) {
            $query->where('store_province_name', 'like', '%' . $value . '%');
        }
    }

    /**
     * 搜索器 - 城市
     * @param $query
     * @param $value
     */
    public function searchStoreCityNameAttr($query, $value)
    {
        if ($value) {
            $query->where('store_city_name', 'like', '%' . $value . '%');
        }
    }

    /**
     * 搜索器 - 云洋快递启用状态
     * @param $query
     * @param $value
     */
    public function searchYunyangEnabledAttr($query, $value)
    {
        if ($value !== '') {
            $query->where('yunyang_enabled', $value);
        }
    }

    /**
     * 修改器 - 处理手机号格式
     * @param $value
     * @return string
     */
    public function setStoreReceiverPhoneAttr($value)
    {
        return trim($value);
    }

    /**
     * 修改器 - 处理客服电话格式
     * @param $value
     * @return string
     */
    public function setServicePhoneAttr($value)
    {
        return trim($value);
    }

    /**
     * 修改器 - 处理地址信息
     * @param $value
     * @return string
     */
    public function setStoreAddressAttr($value)
    {
        return trim($value);
    }

    /**
     * 验证规则
     * @return array
     */
    public function rules()
    {
        return [
            'store_receiver_name' => 'require|max:50',
            'store_receiver_phone' => 'require|mobile',
            'store_province_name' => 'require|max:50',
            'store_city_name' => 'require|max:50',
            'store_district_name' => 'require|max:50',
            'store_address' => 'require|max:200',
            'service_phone' => 'mobile',
            'yunyang_appid' => 'require|max:100',
            'yunyang_key' => 'require|max:200',
        ];
    }

    /**
     * 字段描述
     * @return array
     */
    public function messages()
    {
        return [
            'store_receiver_name.require' => '收货人姓名不能为空',
            'store_receiver_name.max' => '收货人姓名不能超过50个字符',
            'store_receiver_phone.require' => '收货人电话不能为空',
            'store_receiver_phone.mobile' => '收货人电话格式不正确',
            'store_province_name.require' => '省份名称不能为空',
            'store_city_name.require' => '城市名称不能为空',
            'store_district_name.require' => '区县名称不能为空',
            'store_address.require' => '详细地址不能为空',
            'service_phone.mobile' => '客服电话格式不正确',
            'yunyang_appid.require' => '云洋快递AppID不能为空',
            'yunyang_key.require' => '云洋快递Key不能为空',
        ];
    }
}
