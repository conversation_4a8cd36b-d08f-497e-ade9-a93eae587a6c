import request from '@/utils/request'

/**
 * 获取系统配置列表
 */
export function getSystemConfigList(params: Record<string, any>) {
    return request.get(`yz_she/system_config`, {params})
}

/**
 * 获取系统配置详情
 */
export function getSystemConfigInfo(id: number) {
    return request.get(`yz_she/system_config/${id}`)
}

/**
 * 添加系统配置
 */
export function addSystemConfig(params: Record<string, any>) {
    return request.post('yz_she/system_config', params)
}

/**
 * 编辑系统配置
 */
export function editSystemConfig(id: number, params: Record<string, any>) {
    return request.put(`yz_she/system_config/${id}`, params)
}

/**
 * 删除系统配置
 */
export function deleteSystemConfig(id: number) {
    return request.delete(`yz_she/system_config/${id}`)
}

/**
 * 批量删除系统配置
 */
export function batchDeleteSystemConfig(ids: number[]) {
    return request.delete('yz_she/system_config/batch', { data: { ids } })
}

/**
 * 获取当前有效的系统配置
 */
export function getCurrentSystemConfig() {
    return request.get('yz_she/system_config/current')
}

/**
 * 获取收货门店信息
 */
export function getStoreInfo() {
    return request.get('yz_she/system_config/store_info')
}

/**
 * 获取客服信息
 */
export function getServiceInfo() {
    return request.get('yz_she/system_config/service_info')
}

/**
 * 获取云洋快递配置
 */
export function getYunyangConfig() {
    return request.get('yz_she/system_config/yunyang_config')
}

/**
 * 修改云洋快递启用状态
 */
export function changeYunyangStatus(id: number, yunyang_enabled: number) {
    return request.put(`yz_she/system_config/${id}/yunyang_status`, { yunyang_enabled })
}
