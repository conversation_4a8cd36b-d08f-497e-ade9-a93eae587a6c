<?php
// +----------------------------------------------------------------------
// | Niucloud-admin 企业快速开发的多应用管理平台
// +----------------------------------------------------------------------
// | 官方网址：https://www.niucloud.com
// +----------------------------------------------------------------------
// | niucloud团队 版权所有 开源版本可自由商用
// +----------------------------------------------------------------------
// | Author: Niucloud Team
// +----------------------------------------------------------------------

namespace addon\yz_she\app\service\admin\config;

use addon\yz_she\app\model\config\SystemConfig;
use core\base\BaseAdminService;
use core\exception\AdminException;

/**
 * 系统配置服务层
 * Class SystemConfigService
 * @package addon\yz_she\app\service\admin\config
 */
class SystemConfigService extends BaseAdminService
{
    public function __construct()
    {
        parent::__construct();
        $this->model = new SystemConfig();
    }

    /**
     * 获取当前有效的系统配置
     * @return array
     */
    public function getCurrentConfig()
    {
        $field = 'id, store_receiver_name, store_receiver_phone, store_province_name, store_city_name, store_district_name, store_address, service_qr_code, service_phone, yunyang_appid, yunyang_key, yunyang_api_url, yunyang_enabled';

        $info = $this->model->field($field)->order('id desc')->findOrEmpty()->toArray();

        // 如果没有配置，返回默认配置
        if (empty($info)) {
            return $this->getDefaultConfig();
        }

        return $info;
    }

    /**
     * 保存或更新系统配置（单条记录模式）
     * @param array $data
     * @return mixed
     */
    public function saveConfig(array $data)
    {
        // 查找现有配置
        $existConfig = $this->model->order('id desc')->findOrEmpty();

        if ($existConfig->isEmpty()) {
            // 新增配置
            $data['create_time'] = time();
            $data['update_time'] = time();
            $res = $this->model->create($data);
            return $res->id;
        } else {
            // 更新配置
            $data['update_time'] = time();
            $this->model->where([['id', '=', $existConfig->id]])->update($data);
            return $existConfig->id;
        }
    }

    /**
     * 获取默认配置
     * @return array
     */
    private function getDefaultConfig()
    {
        return [
            'id' => 0,
            'store_receiver_name' => '',
            'store_receiver_phone' => '',
            'store_province_name' => '',
            'store_city_name' => '',
            'store_district_name' => '',
            'store_address' => '',
            'service_qr_code' => '',
            'service_phone' => '',
            'yunyang_appid' => '',
            'yunyang_key' => '',
            'yunyang_api_url' => '',
            'yunyang_enabled' => 1
        ];
    }
}
