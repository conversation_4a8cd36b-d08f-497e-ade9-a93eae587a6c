<?php
// +----------------------------------------------------------------------
// | Niucloud-admin 企业快速开发的多应用管理平台
// +----------------------------------------------------------------------
// | 官方网址：https://www.niucloud.com
// +----------------------------------------------------------------------
// | niucloud团队 版权所有 开源版本可自由商用
// +----------------------------------------------------------------------
// | Author: Niucloud Team
// +----------------------------------------------------------------------

namespace addon\yz_she\app\service\admin\config;

use addon\yz_she\app\model\config\SystemConfig;
use core\base\BaseAdminService;
use core\exception\AdminException;

/**
 * 系统配置服务层
 * Class SystemConfigService
 * @package addon\yz_she\app\service\admin\config
 */
class SystemConfigService extends BaseAdminService
{
    public function __construct()
    {
        parent::__construct();
        $this->model = new SystemConfig();
    }

    /**
     * 获取系统配置列表
     * @param array $where
     * @return array
     */
    public function getPage(array $where = [])
    {
        $field = 'id, store_receiver_name, store_receiver_phone, store_province_name, store_city_name, store_district_name, store_address, service_qr_code, service_phone, yunyang_appid, yunyang_key, yunyang_api_url, yunyang_enabled, create_time, update_time';
        $order = 'create_time desc';

        $search_model = $this->model
            ->withSearch(['store_receiver_name', 'store_receiver_phone', 'store_province_name', 'store_city_name', 'yunyang_enabled'], $where)
            ->field($field)
            ->order($order);

        $list = $this->pageQuery($search_model);
        return $list;
    }

    /**
     * 获取系统配置信息
     * @param int $id
     * @return array
     */
    public function getInfo(int $id)
    {
        $field = 'id, store_receiver_name, store_receiver_phone, store_province_name, store_city_name, store_district_name, store_address, service_qr_code, service_phone, yunyang_appid, yunyang_key, yunyang_api_url, yunyang_enabled, create_time, update_time';

        $info = $this->model->field($field)->where([['id', '=', $id]])->findOrEmpty()->toArray();
        if (empty($info)) {
            throw new AdminException('SYSTEM_CONFIG_NOT_EXIST');
        }
        return $info;
    }

    /**
     * 添加系统配置
     * @param array $data
     * @return mixed
     */
    public function add(array $data)
    {
        // 验证数据
        $this->validate($data);
        
        $data['create_time'] = time();
        $data['update_time'] = time();
        $res = $this->model->create($data);
        return $res->id;
    }

    /**
     * 系统配置编辑
     * @param int $id
     * @param array $data
     * @return bool
     */
    public function edit(int $id, array $data)
    {
        // 验证数据
        $this->validate($data);
        
        $this->model->where([['id', '=', $id]])->update($data);
        return true;
    }

    /**
     * 删除系统配置
     * @param int $id
     * @return bool
     */
    public function del(int $id)
    {
        $model = $this->model->where([['id', '=', $id]])->findOrEmpty();
        if ($model->isEmpty()) {
            throw new AdminException('SYSTEM_CONFIG_NOT_EXIST');
        }
        $res = $model->delete();
        return $res;
    }

    /**
     * 获取当前有效的系统配置
     * @return array
     */
    public function getCurrentConfig()
    {
        $field = 'id, store_receiver_name, store_receiver_phone, store_province_name, store_city_name, store_district_name, store_address, service_qr_code, service_phone, yunyang_appid, yunyang_key, yunyang_api_url, yunyang_enabled';

        $info = $this->model->field($field)->order('id desc')->findOrEmpty()->toArray();

        // 如果没有配置，返回默认配置
        if (empty($info)) {
            return $this->getDefaultConfig();
        }

        return $info;
    }

    /**
     * 保存或更新系统配置（单条记录模式）
     * @param array $data
     * @return mixed
     */
    public function saveConfig(array $data)
    {
        // 查找现有配置
        $existConfig = $this->model->order('id desc')->findOrEmpty();

        if ($existConfig->isEmpty()) {
            // 新增配置
            $data['create_time'] = time();
            $data['update_time'] = time();
            $res = $this->model->create($data);
            return $res->id;
        } else {
            // 更新配置
            $data['update_time'] = time();
            $this->model->where([['id', '=', $existConfig->id]])->update($data);
            return $existConfig->id;
        }
    }

    /**
     * 获取默认配置
     * @return array
     */
    private function getDefaultConfig()
    {
        return [
            'id' => 0,
            'store_receiver_name' => '',
            'store_receiver_phone' => '',
            'store_province_name' => '',
            'store_city_name' => '',
            'store_district_name' => '',
            'store_address' => '',
            'service_qr_code' => '',
            'service_phone' => '',
            'yunyang_appid' => '',
            'yunyang_key' => '',
            'yunyang_api_url' => '',
            'yunyang_enabled' => 1
        ];
    }

    /**
     * 验证数据
     * @param array $data
     * @return bool
     */
    private function validate(array $data)
    {
        // 验证收货人姓名
        if (empty($data['store_receiver_name'])) {
            throw new AdminException('收货人姓名不能为空');
        }
        if (mb_strlen($data['store_receiver_name']) > 50) {
            throw new AdminException('收货人姓名不能超过50个字符');
        }

        // 验证收货人电话
        if (empty($data['store_receiver_phone'])) {
            throw new AdminException('收货人电话不能为空');
        }
        if (!preg_match('/^1[3-9]\d{9}$/', $data['store_receiver_phone'])) {
            throw new AdminException('收货人电话格式不正确');
        }

        // 验证省份
        if (empty($data['store_province_name'])) {
            throw new AdminException('省份名称不能为空');
        }

        // 验证城市
        if (empty($data['store_city_name'])) {
            throw new AdminException('城市名称不能为空');
        }

        // 验证区县
        if (empty($data['store_district_name'])) {
            throw new AdminException('区县名称不能为空');
        }

        // 验证详细地址
        if (empty($data['store_address'])) {
            throw new AdminException('详细地址不能为空');
        }

        // 验证客服电话（可选）
        if (!empty($data['service_phone']) && !preg_match('/^1[3-9]\d{9}$/', $data['service_phone'])) {
            throw new AdminException('客服电话格式不正确');
        }

        // 验证云洋快递配置
        if (empty($data['yunyang_appid'])) {
            throw new AdminException('云洋快递AppID不能为空');
        }
        if (empty($data['yunyang_key'])) {
            throw new AdminException('云洋快递Key不能为空');
        }

        return true;
    }

    /**
     * 获取收货门店信息
     * @return array
     */
    public function getStoreInfo()
    {
        $config = $this->getCurrentConfig();
        return [
            'store_receiver_name' => $config['store_receiver_name'],
            'store_receiver_phone' => $config['store_receiver_phone'],
            'store_province_name' => $config['store_province_name'],
            'store_city_name' => $config['store_city_name'],
            'store_district_name' => $config['store_district_name'],
            'store_address' => $config['store_address']
        ];
    }

    /**
     * 获取客服信息
     * @return array
     */
    public function getServiceInfo()
    {
        $config = $this->getCurrentConfig();
        return [
            'service_qr_code' => $config['service_qr_code'],
            'service_phone' => $config['service_phone']
        ];
    }

    /**
     * 获取云洋快递配置
     * @return array
     */
    public function getYunyangConfig()
    {
        $config = $this->getCurrentConfig();
        return [
            'yunyang_appid' => $config['yunyang_appid'],
            'yunyang_key' => $config['yunyang_key'],
            'yunyang_api_url' => $config['yunyang_api_url'],
            'yunyang_enabled' => $config['yunyang_enabled']
        ];
    }
}
