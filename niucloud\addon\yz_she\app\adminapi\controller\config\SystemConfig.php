<?php
// +----------------------------------------------------------------------
// | Niucloud-admin 企业快速开发的多应用管理平台
// +----------------------------------------------------------------------
// | 官方网址：https://www.niucloud.com
// +----------------------------------------------------------------------
// | niucloud团队 版权所有 开源版本可自由商用
// +----------------------------------------------------------------------
// | Author: Niucloud Team
// +----------------------------------------------------------------------

namespace addon\yz_she\app\adminapi\controller\config;

use addon\yz_she\app\service\admin\config\SystemConfigService;
use core\base\BaseAdminController;
use think\Response;

/**
 * 系统配置控制器
 * Class SystemConfig
 * @package addon\yz_she\app\adminapi\controller\config
 */
class SystemConfig extends BaseAdminController
{
    /**
     * 获取系统配置列表
     * @return Response
     */
    public function lists()
    {
        $data = $this->request->params([
            ['store_receiver_name', ''],
            ['store_receiver_phone', ''],
            ['store_province_name', ''],
            ['store_city_name', ''],
            ['yunyang_enabled', '']
        ]);
        return success((new SystemConfigService())->getPage($data));
    }

    /**
     * 系统配置详情
     * @param int $id
     * @return Response
     */
    public function info(int $id)
    {
        return success((new SystemConfigService())->getInfo($id));
    }

    /**
     * 添加系统配置
     * @return Response
     */
    public function add()
    {
        $data = $this->request->params([
            ['store_receiver_name', ''],
            ['store_receiver_phone', ''],
            ['store_province_name', ''],
            ['store_city_name', ''],
            ['store_district_name', ''],
            ['store_address', ''],
            ['service_qr_code', ''],
            ['service_phone', ''],
            ['yunyang_appid', ''],
            ['yunyang_key', ''],
            ['yunyang_api_url', ''],
            ['yunyang_enabled', 1]
        ]);
        $id = (new SystemConfigService())->add($data);
        return success('ADD_SUCCESS', ['id' => $id]);
    }

    /**
     * 系统配置编辑
     * @param int $id
     * @return Response
     */
    public function edit(int $id)
    {
        $data = $this->request->params([
            ['store_receiver_name', ''],
            ['store_receiver_phone', ''],
            ['store_province_name', ''],
            ['store_city_name', ''],
            ['store_district_name', ''],
            ['store_address', ''],
            ['service_qr_code', ''],
            ['service_phone', ''],
            ['yunyang_appid', ''],
            ['yunyang_key', ''],
            ['yunyang_api_url', ''],
            ['yunyang_enabled', 1]
        ]);
        (new SystemConfigService())->edit($id, $data);
        return success('EDIT_SUCCESS');
    }

    /**
     * 系统配置删除
     * @param int $id
     * @return Response
     */
    public function del(int $id)
    {
        (new SystemConfigService())->del($id);
        return success('DELETE_SUCCESS');
    }

    /**
     * 获取当前有效的系统配置
     * @return Response
     */
    public function current()
    {
        return success((new SystemConfigService())->getCurrentConfig());
    }

    /**
     * 获取收货门店信息
     * @return Response
     */
    public function storeInfo()
    {
        return success((new SystemConfigService())->getStoreInfo());
    }

    /**
     * 获取客服信息
     * @return Response
     */
    public function serviceInfo()
    {
        return success((new SystemConfigService())->getServiceInfo());
    }

    /**
     * 获取云洋快递配置
     * @return Response
     */
    public function yunyangConfig()
    {
        return success((new SystemConfigService())->getYunyangConfig());
    }

    /**
     * 批量删除
     * @return Response
     */
    public function batchDel()
    {
        $data = $this->request->params([
            ['ids', []]
        ]);
        
        if (empty($data['ids'])) {
            return fail('请选择要删除的数据');
        }
        
        $service = new SystemConfigService();
        foreach ($data['ids'] as $id) {
            $service->del($id);
        }
        
        return success('DELETE_SUCCESS');
    }

    /**
     * 修改云洋快递启用状态
     * @param int $id
     * @return Response
     */
    public function changeYunyangStatus(int $id)
    {
        $data = $this->request->params([
            ['yunyang_enabled', 1]
        ]);
        
        $service = new SystemConfigService();
        $info = $service->getInfo($id);
        
        $service->edit($id, [
            'store_receiver_name' => $info['store_receiver_name'],
            'store_receiver_phone' => $info['store_receiver_phone'],
            'store_province_name' => $info['store_province_name'],
            'store_city_name' => $info['store_city_name'],
            'store_district_name' => $info['store_district_name'],
            'store_address' => $info['store_address'],
            'service_qr_code' => $info['service_qr_code'],
            'service_phone' => $info['service_phone'],
            'yunyang_appid' => $info['yunyang_appid'],
            'yunyang_key' => $info['yunyang_key'],
            'yunyang_api_url' => $info['yunyang_api_url'],
            'yunyang_enabled' => $data['yunyang_enabled']
        ]);
        
        return success('EDIT_SUCCESS');
    }
}
