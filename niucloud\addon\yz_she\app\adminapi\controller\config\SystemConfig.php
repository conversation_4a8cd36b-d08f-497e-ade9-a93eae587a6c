<?php
// +----------------------------------------------------------------------
// | Niucloud-admin 企业快速开发的多应用管理平台
// +----------------------------------------------------------------------
// | 官方网址：https://www.niucloud.com
// +----------------------------------------------------------------------
// | niucloud团队 版权所有 开源版本可自由商用
// +----------------------------------------------------------------------
// | Author: Niucloud Team
// +----------------------------------------------------------------------

namespace addon\yz_she\app\adminapi\controller\config;

use addon\yz_she\app\service\admin\config\SystemConfigService;
use core\base\BaseAdminController;
use think\Response;

/**
 * 系统配置控制器
 * Class SystemConfig
 * @package addon\yz_she\app\adminapi\controller\config
 */
class SystemConfig extends BaseAdminController
{
    /**
     * 获取当前有效的系统配置
     * @return Response
     */
    public function current()
    {
        return success((new SystemConfigService())->getCurrentConfig());
    }

    /**
     * 保存系统配置（单条记录模式）
     * @return Response
     */
    public function save()
    {
        $data = $this->request->params([
            ['store_receiver_name', ''],
            ['store_receiver_phone', ''],
            ['store_province_name', ''],
            ['store_city_name', ''],
            ['store_district_name', ''],
            ['store_address', ''],
            ['service_qr_code', ''],
            ['service_phone', ''],
            ['yunyang_appid', ''],
            ['yunyang_key', ''],
            ['yunyang_api_url', ''],
            ['yunyang_enabled', 1]
        ]);
        $id = (new SystemConfigService())->saveConfig($data);
        return success('SAVE_SUCCESS', ['id' => $id]);
    }
}
