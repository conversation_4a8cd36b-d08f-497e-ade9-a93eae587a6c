<template>
    <div class="main-container">
        <el-form :model="formData" label-width="120px" ref="formRef" :rules="formRules" class="page-form">
            <div class="config-section">
                <h3 class="section-title">收货门店信息</h3>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="收货人姓名" prop="store_receiver_name">
                            <el-input v-model="formData.store_receiver_name" placeholder="请输入收货人姓名" clearable />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="收货人电话" prop="store_receiver_phone">
                            <el-input v-model="formData.store_receiver_phone" placeholder="请输入收货人电话" clearable />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="8">
                        <el-form-item label="省份" prop="store_province_name">
                            <el-input v-model="formData.store_province_name" placeholder="请输入省份" clearable />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="城市" prop="store_city_name">
                            <el-input v-model="formData.store_city_name" placeholder="请输入城市" clearable />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="区县" prop="store_district_name">
                            <el-input v-model="formData.store_district_name" placeholder="请输入区县" clearable />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-form-item label="详细地址" prop="store_address">
                    <el-input v-model="formData.store_address" type="textarea" :rows="3" placeholder="请输入详细地址" />
                </el-form-item>
            </div>

            <div class="config-section">
                <h3 class="section-title">平台客服信息</h3>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="客服电话" prop="service_phone">
                            <el-input v-model="formData.service_phone" placeholder="请输入客服电话" clearable />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="客服二维码" prop="service_qr_code">
                            <upload-image
                                v-model="formData.service_qr_code"
                                :limit="1"
                                :size="1024 * 2"
                            />
                        </el-form-item>
                    </el-col>
                </el-row>
            </div>

            <div class="config-section">
                <h3 class="section-title">云洋快递配置</h3>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="AppID" prop="yunyang_appid">
                            <el-input v-model="formData.yunyang_appid" placeholder="请输入云洋快递AppID" clearable />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="Key" prop="yunyang_key">
                            <el-input v-model="formData.yunyang_key" type="password" placeholder="请输入云洋快递Key" clearable show-password />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="回调API地址" prop="yunyang_api_url">
                            <el-input v-model="formData.yunyang_api_url" placeholder="请输入回调API地址" clearable />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="是否自动发单" prop="yunyang_enabled">
                            <el-switch
                                v-model="formData.yunyang_enabled"
                                :active-value="1"
                                :inactive-value="0"
                                active-text="启用"
                                inactive-text="禁用"
                            />
                        </el-form-item>
                    </el-col>
                </el-row>
            </div>
        </el-form>

        <div class="fixed-footer">
            <div class="fixed-footer-wrap">
                <div class="flex justify-end">
                    <el-button @click="$emit('complete')">取消</el-button>
                    <el-button type="primary" :loading="loading" @click="save">保存</el-button>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { reactive, ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { addSystemConfig, editSystemConfig, getSystemConfigInfo } from '@/addon/yz_she/api/system_config'

const props = defineProps({
    type: {
        type: String,
        default: ''
    },
    data: {
        type: Object,
        default: () => ({})
    }
})

const emit = defineEmits(['complete'])

const loading = ref(false)
const formRef = ref()

// 表单数据
const formData = reactive({
    store_receiver_name: '',
    store_receiver_phone: '',
    store_province_name: '',
    store_city_name: '',
    store_district_name: '',
    store_address: '',
    service_qr_code: '',
    service_phone: '',
    yunyang_appid: '',
    yunyang_key: '',
    yunyang_api_url: '',
    yunyang_enabled: 1
})

// 表单验证规则
const formRules = reactive({
    store_receiver_name: [
        { required: true, message: '收货人姓名不能为空', trigger: 'blur' },
        { max: 50, message: '收货人姓名不能超过50个字符', trigger: 'blur' }
    ],
    store_receiver_phone: [
        { required: true, message: '收货人电话不能为空', trigger: 'blur' },
        { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
    ],
    store_province_name: [
        { required: true, message: '省份不能为空', trigger: 'blur' },
        { max: 50, message: '省份名称不能超过50个字符', trigger: 'blur' }
    ],
    store_city_name: [
        { required: true, message: '城市不能为空', trigger: 'blur' },
        { max: 50, message: '城市名称不能超过50个字符', trigger: 'blur' }
    ],
    store_district_name: [
        { required: true, message: '区县不能为空', trigger: 'blur' },
        { max: 50, message: '区县名称不能超过50个字符', trigger: 'blur' }
    ],
    store_address: [
        { required: true, message: '详细地址不能为空', trigger: 'blur' },
        { max: 200, message: '详细地址不能超过200个字符', trigger: 'blur' }
    ],
    service_phone: [
        { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
    ],
    yunyang_appid: [
        { required: true, message: '云洋快递AppID不能为空', trigger: 'blur' },
        { max: 100, message: 'AppID不能超过100个字符', trigger: 'blur' }
    ],
    yunyang_key: [
        { required: true, message: '云洋快递Key不能为空', trigger: 'blur' },
        { max: 200, message: 'Key不能超过200个字符', trigger: 'blur' }
    ],
    yunyang_api_url: [
        { max: 200, message: 'API地址不能超过200个字符', trigger: 'blur' }
    ]
})

const mode = computed(() => {
    return props.type === 'edit' ? 'edit' : 'add'
})

/**
 * 保存
 */
const save = async () => {
    await formRef.value.validate()
    loading.value = true

    const saveData = { ...formData }

    if (mode.value === 'edit') {
        editSystemConfig(props.data.id, saveData).then(() => {
            loading.value = false
            ElMessage.success('编辑成功')
            emit('complete')
        }).catch(() => {
            loading.value = false
        })
    } else {
        addSystemConfig(saveData).then(() => {
            loading.value = false
            ElMessage.success('添加成功')
            emit('complete')
        }).catch(() => {
            loading.value = false
        })
    }
}

/**
 * 获取详情
 */
const getDetail = async () => {
    if (mode.value === 'edit' && props.data.id) {
        const data = await getSystemConfigInfo(props.data.id)
        Object.assign(formData, data.data)
    }
}

getDetail()
</script>

<style lang="scss" scoped>
.config-section {
    margin-bottom: 30px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;

    .section-title {
        margin: 0 0 20px 0;
        font-size: 16px;
        font-weight: 600;
        color: #303133;
        border-left: 4px solid #409eff;
        padding-left: 10px;
    }
}

.fixed-footer {
    position: fixed;
    bottom: 0;
    right: 0;
    left: 0;
    z-index: 100;
    background: #fff;
    border-top: 1px solid #e4e7ed;
    padding: 16px 0;

    .fixed-footer-wrap {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 20px;
    }
}

.page-form {
    padding-bottom: 80px;
}
</style>
