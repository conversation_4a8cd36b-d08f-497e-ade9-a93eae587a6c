-- ===========================
-- 插件 wj_she 系统配置表
-- ===========================

DROP TABLE IF EXISTS `www_yz_she_system_config`;
CREATE TABLE `www_yz_she_system_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',

  -- 收货门店信息
  `store_receiver_name` varchar(50) NOT NULL DEFAULT '' COMMENT '收货人姓名',
  `store_receiver_phone` varchar(20) NOT NULL DEFAULT '' COMMENT '收货人电话',
  `store_province_name` varchar(50) NOT NULL DEFAULT '' COMMENT '省份名称',
  `store_city_name` varchar(50) NOT NULL DEFAULT '' COMMENT '城市名称',
  `store_district_name` varchar(50) NOT NULL DEFAULT '' COMMENT '区县名称',
  `store_address` varchar(200) NOT NULL DEFAULT '' COMMENT '详细地址',

  -- 平台客服信息
  `service_qr_code` varchar(500) NOT NULL DEFAULT '' COMMENT '客服二维码',
  `service_phone` varchar(20) NOT NULL DEFAULT '' COMMENT '客服电话',

  -- 云洋快递配置
  `yunyang_appid` varchar(100) NOT NULL DEFAULT '' COMMENT '云洋快递AppID',
  `yunyang_key` varchar(200) NOT NULL DEFAULT '' COMMENT '云洋快递Key',
  `yunyang_api_url` varchar(200) NOT NULL DEFAULT '' COMMENT '回调API地址',
  `yunyang_enabled` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否自动发单 0:禁用 1:启用',

  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',

  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='奢侈品系统配置表';
