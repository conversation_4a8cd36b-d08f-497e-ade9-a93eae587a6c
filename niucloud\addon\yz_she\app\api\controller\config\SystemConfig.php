<?php
// +----------------------------------------------------------------------
// | Niucloud-admin 企业快速开发的多应用管理平台
// +----------------------------------------------------------------------
// | 官方网址：https://www.niucloud.com
// +----------------------------------------------------------------------
// | niucloud团队 版权所有 开源版本可自由商用
// +----------------------------------------------------------------------
// | Author: Niucloud Team
// +----------------------------------------------------------------------

namespace addon\yz_she\app\api\controller\config;

use addon\yz_she\app\service\api\config\SystemConfigService;
use core\base\BaseApiController;
use think\Response;

/**
 * 系统配置API控制器
 * Class SystemConfig
 * @package addon\yz_she\app\api\controller\config
 */
class SystemConfig extends BaseApiController
{
    /**
     * 获取当前有效的系统配置
     * @return Response
     */
    public function current()
    {
        return success((new SystemConfigService())->getCurrentConfig());
    }

    /**
     * 获取收货门店信息
     * @return Response
     */
    public function storeInfo()
    {
        return success((new SystemConfigService())->getStoreInfo());
    }

    /**
     * 获取客服信息
     * @return Response
     */
    public function serviceInfo()
    {
        return success((new SystemConfigService())->getServiceInfo());
    }

    /**
     * 获取云洋快递配置
     * @return Response
     */
    public function yunyangConfig()
    {
        return success((new SystemConfigService())->getYunyangConfig());
    }

    /**
     * 获取完整收货地址
     * @return Response
     */
    public function fullStoreAddress()
    {
        return success([
            'address' => (new SystemConfigService())->getFullStoreAddress()
        ]);
    }

    /**
     * 检查云洋快递是否启用
     * @return Response
     */
    public function yunyangEnabled()
    {
        return success([
            'enabled' => (new SystemConfigService())->isYunyangEnabled()
        ]);
    }
}
