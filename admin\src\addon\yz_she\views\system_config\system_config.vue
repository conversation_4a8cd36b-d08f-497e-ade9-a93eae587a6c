<template>
    <div class="main-container">
        <el-card class="box-card !border-none" shadow="never">
            <div class="flex justify-between items-center">
                <span class="text-page-title">{{ t('systemConfigManagement') }}</span>
                <el-button type="primary" @click="addEvent">
                    <template #icon>
                        <icon name="element-Plus" />
                    </template>
                    {{ t('addConfig') }}
                </el-button>
            </div>

            <el-card class="box-card !border-none my-[10px] table-search-wrap" shadow="never">
                <el-form :model="systemConfigTable.searchParam" label-width="90px" :inline="true">
                    <el-form-item label="收货人姓名" prop="store_receiver_name">
                        <el-input v-model="systemConfigTable.searchParam.store_receiver_name" placeholder="请输入收货人姓名" />
                    </el-form-item>
                    <el-form-item label="收货人电话" prop="store_receiver_phone">
                        <el-input v-model="systemConfigTable.searchParam.store_receiver_phone" placeholder="请输入收货人电话" />
                    </el-form-item>
                    <el-form-item label="省份" prop="store_province_name">
                        <el-input v-model="systemConfigTable.searchParam.store_province_name" placeholder="请输入省份" />
                    </el-form-item>
                    <el-form-item label="城市" prop="store_city_name">
                        <el-input v-model="systemConfigTable.searchParam.store_city_name" placeholder="请输入城市" />
                    </el-form-item>
                    <el-form-item label="云洋快递" prop="yunyang_enabled">
                        <el-select v-model="systemConfigTable.searchParam.yunyang_enabled" placeholder="请选择状态" clearable>
                            <el-option label="启用" :value="1" />
                            <el-option label="禁用" :value="0" />
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="loadSystemConfigList()">搜索</el-button>
                        <el-button @click="resetForm">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-card>

            <div class="mt-[10px]">
                <el-table :data="systemConfigTable.data" size="large" v-loading="systemConfigTable.loading">
                    <template #empty>
                        <span>暂无数据</span>
                    </template>
                    <el-table-column prop="store_receiver_name" label="收货人姓名" min-width="120" />
                    <el-table-column prop="store_receiver_phone" label="收货人电话" min-width="130" />
                    <el-table-column label="收货地址" min-width="200">
                        <template #default="{ row }">
                            {{ row.store_province_name }}{{ row.store_city_name }}{{ row.store_district_name }}{{ row.store_address }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="service_phone" label="客服电话" min-width="130" />
                    <el-table-column prop="yunyang_appid" label="云洋AppID" min-width="150" show-overflow-tooltip />
                    <el-table-column label="云洋快递" min-width="100" align="center">
                        <template #default="{ row }">
                            <el-switch
                                v-model="row.yunyang_enabled"
                                :active-value="1"
                                :inactive-value="0"
                                @change="changeYunyangStatus(row)"
                            />
                        </template>
                    </el-table-column>
                    <el-table-column prop="create_time" label="创建时间" min-width="180" />
                    <el-table-column label="操作" width="130" align="right" fixed="right">
                        <template #default="{ row }">
                            <el-button type="primary" link @click="editEvent(row)">编辑</el-button>
                            <el-button type="primary" link @click="deleteEvent(row.id)">删除</el-button>
                        </template>
                    </el-table-column>
                </el-table>
                <div class="mt-[16px] flex justify-end">
                    <el-pagination
                        v-model:current-page="systemConfigTable.page"
                        v-model:page-size="systemConfigTable.limit"
                        layout="total, sizes, prev, pager, next, jumper"
                        :total="systemConfigTable.total"
                        @size-change="loadSystemConfigList()"
                        @current-change="loadSystemConfigList()"
                    />
                </div>
            </div>
        </el-card>

        <!-- 添加/编辑弹窗 -->
        <el-dialog v-model="showDialog" :title="dialogTitle" width="800px" :destroy-on-close="true">
            <system-config-edit
                ref="systemConfigEditRef"
                :type="dialogType"
                :data="dialogData"
                @complete="completeEvent"
            />
        </el-dialog>
    </div>
</template>

<script lang="ts" setup>
import { reactive, ref } from 'vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import { t } from '@/lang'
import { getSystemConfigList, deleteSystemConfig, changeYunyangStatus } from '@/addon/yz_she/api/system_config'
import SystemConfigEdit from './components/system-config-edit.vue'

const systemConfigTable = reactive({
    page: 1,
    limit: 10,
    total: 0,
    loading: true,
    data: [],
    searchParam: {
        store_receiver_name: '',
        store_receiver_phone: '',
        store_province_name: '',
        store_city_name: '',
        yunyang_enabled: ''
    }
})

const showDialog = ref(false)
const dialogType = ref('')
const dialogTitle = ref('')
const dialogData = ref({})
const systemConfigEditRef = ref()

/**
 * 获取系统配置列表
 */
const loadSystemConfigList = (page: number = 1) => {
    systemConfigTable.loading = true
    systemConfigTable.page = page

    getSystemConfigList({
        page: systemConfigTable.page,
        limit: systemConfigTable.limit,
        ...systemConfigTable.searchParam
    }).then(res => {
        systemConfigTable.loading = false
        systemConfigTable.data = res.data.data
        systemConfigTable.total = res.data.total
    }).catch(() => {
        systemConfigTable.loading = false
    })
}

/**
 * 添加系统配置
 */
const addEvent = () => {
    dialogType.value = 'add'
    dialogTitle.value = t('addConfig')
    dialogData.value = {}
    showDialog.value = true
}

/**
 * 编辑系统配置
 */
const editEvent = (data: any) => {
    dialogType.value = 'edit'
    dialogTitle.value = t('editConfig')
    dialogData.value = data
    showDialog.value = true
}

/**
 * 删除系统配置
 */
const deleteEvent = (id: number) => {
    ElMessageBox.confirm('确认删除该配置？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
        deleteSystemConfig(id).then(() => {
            ElMessage.success('删除成功')
            loadSystemConfigList()
        })
    })
}

/**
 * 修改云洋快递状态
 */
const changeYunyangStatus = (row: any) => {
    changeYunyangStatus(row.id, row.yunyang_enabled).then(() => {
        ElMessage.success('状态修改成功')
    }).catch(() => {
        // 恢复原状态
        row.yunyang_enabled = row.yunyang_enabled === 1 ? 0 : 1
    })
}

/**
 * 完成事件
 */
const completeEvent = () => {
    showDialog.value = false
    loadSystemConfigList()
}

/**
 * 重置搜索表单
 */
const resetForm = () => {
    systemConfigTable.searchParam = {
        store_receiver_name: '',
        store_receiver_phone: '',
        store_province_name: '',
        store_city_name: '',
        yunyang_enabled: ''
    }
    loadSystemConfigList()
}

loadSystemConfigList()
</script>

<style lang="scss" scoped></style>
